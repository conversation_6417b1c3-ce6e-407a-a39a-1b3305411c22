{"name": "interview-p<PERSON><PERSON>", "private": true, "version": "0.0.0", "type": "module", "scripts": {"dev": "vite", "build": "tsc -b && vite build && cp ./manifest.json dist/manifest.json", "lint": "eslint .", "preview": "vite preview"}, "dependencies": {"@ai-sdk/openai": "^1.3.20", "@radix-ui/react-dialog": "^1.1.14", "@radix-ui/react-label": "^2.1.4", "@radix-ui/react-radio-group": "^1.3.4", "@radix-ui/react-scroll-area": "^1.2.6", "@radix-ui/react-select": "^2.2.5", "@radix-ui/react-separator": "^1.1.4", "@radix-ui/react-slot": "^1.2.0", "@radix-ui/react-tooltip": "^1.2.4", "@tailwindcss/vite": "^4.1.4", "ai": "^4.3.10", "class-variance-authority": "^0.7.1", "clsx": "^2.1.1", "html2pdf.js": "^0.10.3", "jspdf": "^3.0.1", "lucide-react": "^0.503.0", "next-themes": "^0.4.6", "openai": "^4.96.0", "pdfjs-dist": "^4.8.69", "prism-react-renderer": "^2.4.1", "react": "^19.0.0", "react-dom": "^19.0.0", "redact-pii": "^3.4.0", "sonner": "^2.0.3", "tailwind-merge": "^3.2.0", "tailwindcss": "^4.1.4"}, "devDependencies": {"@eslint/js": "^9.22.0", "@types/node": "^22.15.2", "@types/react": "^19.0.10", "@types/react-dom": "^19.0.4", "@vitejs/plugin-react": "^4.3.4", "eslint": "^9.22.0", "eslint-plugin-react-hooks": "^5.2.0", "eslint-plugin-react-refresh": "^0.4.19", "globals": "^16.0.0", "tw-animate-css": "^1.2.8", "typescript": "~5.7.2", "typescript-eslint": "^8.26.1", "vite": "^6.3.1"}}